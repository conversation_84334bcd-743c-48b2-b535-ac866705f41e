<div class="min-h-screen bg-white text-gray-800 flex flex-col">
  
  <!-- Enhanced professional header with breadcrumb and quick access -->
  <header class="bg-gray-950 shadow-xl border-b-4 border-orange-500 relative z-10">
    <div class="container mx-auto px-4 sm:px-6 py-3 sm:py-4">
      <!-- Main header row -->
      <div class="flex justify-between items-center">
        <!-- Logo section -->
        <div class="flex-1 flex justify-start items-center">
          <a routerLink="/" class="hover:opacity-80 transition duration-300 flex items-center">
            <img
              src="assets/images/BcLogo.png"
              alt="Benedicto College Logo"
              class="h-10 sm:h-14 md:h-16 lg:h-20 w-auto max-w-full object-contain"
              onerror="console.error('Logo failed to load:', this.src); this.style.border='2px solid red';"
              onload="console.log('Logo loaded successfully:', this.src);"
            >
          </a>
        </div>

        <!-- Breadcrumb navigation (hidden on mobile) -->
        <div class="hidden lg:flex flex-1 justify-center">
          <nav class="flex items-center space-x-2 text-sm">
            <a routerLink="/" class="text-gray-300 hover:text-orange-400 transition duration-300">Home</a>
            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <span class="text-orange-400 font-medium">About Us</span>
          </nav>
        </div>

        <!-- Navigation and quick access -->
        <div class="flex items-center space-x-2 lg:space-x-4">
          <!-- Quick access links with tooltips - always visible -->
          <div class="flex items-center space-x-4 md:space-x-6">
            <!-- Main Website -->
            <div class="relative group">
              <a href="https://benedictocollege.edu.ph" target="_blank" class="text-gray-300 hover:text-orange-400 transition duration-300 p-2 rounded-lg hover:bg-gray-800 flex items-center">
                <svg class="w-6 h-6 text-black" fill="none" stroke="black" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
                </svg>
              </a>
              <!-- Tooltip -->
              <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-50">
                Main Website
                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
              </div>
            </div>

            <!-- Help & Support -->
            <div class="relative group">
              <a routerLink="/support" class="text-gray-300 hover:text-orange-400 transition duration-300 p-2 rounded-lg hover:bg-gray-800 flex items-center">
                <svg class="w-6 h-6 text-black" fill="none" stroke="black" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </a>
              <!-- Tooltip -->
              <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-50">
                Help & Support
                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
              </div>
            </div>

            <!-- About Us -->
            <div class="relative group">
              <a routerLink="/about" class="text-gray-300 hover:text-orange-400 transition duration-300 p-2 rounded-lg hover:bg-gray-800 flex items-center">
                <svg class="w-6 h-6 text-black" fill="none" stroke="black" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </a>
              <!-- Tooltip -->
              <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-50">
                About Us
                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
              </div>
            </div>

            <!-- Contact Us -->
            <div class="relative group">
              <a routerLink="/contact" class="text-gray-300 hover:text-orange-400 transition duration-300 p-2 rounded-lg hover:bg-gray-800 flex items-center">
                <svg class="w-6 h-6 text-black" fill="none" stroke="black" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </a>
              <!-- Tooltip -->
              <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-50">
                Contact Us
                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
              </div>
            </div>

            <!-- My Account -->
            <div class="relative group">
              <a routerLink="/login" class="text-gray-300 hover:text-orange-400 transition duration-300 p-2 rounded-lg hover:bg-gray-800 flex items-center">
                <svg class="w-6 h-6 text-black" fill="none" stroke="black" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </a>
              <!-- Tooltip -->
              <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-50">
                My Account
                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
              </div>
            </div>
          </div>

          <!-- Main navigation -->
          <nav class="hidden md:flex space-x-4 lg:space-x-6">
            <a routerLink="/about" class="text-orange-400 font-semibold px-3 lg:px-4 py-2 rounded-lg bg-gray-800">About us</a>
            <a routerLink="/contact" class="text-white hover:text-orange-400 transition duration-300 px-3 lg:px-4 py-2 rounded-lg hover:bg-gray-800">Contact us</a>
          </nav>

          <!-- Login dropdown -->
          <div class="hidden md:block relative">
            <button id="login-dropdown-button" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition duration-300 flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
              Login
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div id="login-dropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
              <a routerLink="/login" class="block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-orange-600 transition duration-300 rounded-t-lg">
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
                Student Portal
              </a>
              <a routerLink="/faculty-login" class="block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-orange-600 transition duration-300">
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                Faculty Portal
              </a>
              <a routerLink="/admin-login" class="block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-orange-600 transition duration-300 rounded-b-lg">
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                Admin Portal
              </a>
            </div>
          </div>

          <!-- Mobile menu button -->
          <div class="md:hidden">
            <button id="mobile-menu-button" class="text-white focus:outline-none p-2">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Mobile menu -->
      <div id="mobile-menu" class="hidden md:hidden border-t border-gray-700 mt-4 pt-4">
        <!-- Mobile breadcrumb -->
        <div class="mb-4 text-sm">
          <nav class="flex items-center space-x-2">
            <a routerLink="/" class="text-gray-300 hover:text-orange-400 transition duration-300">Home</a>
            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <span class="text-orange-400 font-medium">About Us</span>
          </nav>
        </div>

        <!-- Mobile navigation -->
        <div class="space-y-2">
          <a routerLink="/about" class="block py-2 px-4 text-sm bg-gray-800 text-orange-400 rounded font-semibold">About us</a>
          <a routerLink="/contact" class="block py-2 px-4 text-sm hover:bg-gray-800 text-white rounded">Contact us</a>
          <a routerLink="/support" class="block py-2 px-4 text-sm hover:bg-gray-800 text-white rounded">Help & Support</a>
          <a href="https://benedictocollege.edu.ph" target="_blank" class="block py-2 px-4 text-sm hover:bg-gray-800 text-white rounded">Main Website</a>

          <!-- Mobile login section -->
          <div class="border-t border-gray-700 pt-4 mt-4">
            <p class="text-gray-400 text-xs uppercase tracking-wide mb-2 px-4">Login Portals</p>
            <a routerLink="/login" class="block py-2 px-4 text-sm hover:bg-gray-800 text-white rounded">Student Portal</a>
            <a routerLink="/faculty-login" class="block py-2 px-4 text-sm hover:bg-gray-800 text-white rounded">Faculty Portal</a>
            <a routerLink="/admin-login" class="block py-2 px-4 text-sm hover:bg-gray-800 text-white rounded">Admin Portal</a>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <main class="flex-1 py-16 px-4 sm:px-6">
    <div class="about-container">
      
      <!-- Hero Section with Video Background -->
      <div class="hero-section relative overflow-hidden">
        <!-- Video Background -->
        <div class="absolute inset-0 z-0">
          <video
            autoplay
            muted
            loop
            playsinline
            class="w-full h-full object-cover opacity-30"
            poster="assets/images/hero-fallback.jpg">
            <source src="assets/videos/campus-life.mp4" type="video/mp4">
            <source src="assets/videos/campus-life.webm" type="video/webm">
            <!-- Fallback for browsers that don't support video -->
            <img src="assets/images/hero-fallback.jpg" alt="Benedicto College Campus" class="w-full h-full object-cover">
          </video>
          <!-- Video overlay -->
          <div class="absolute inset-0 bg-gradient-to-r from-gray-900/80 to-gray-800/60"></div>
        </div>

        <!-- Hero Content -->
        <div class="relative z-10">
          <h1 class="text-4xl md:text-6xl font-bold mb-6 animate-fade-in-up">About Benedicto College</h1>
          <p class="text-xl md:text-2xl mb-8 opacity-90 animate-fade-in-up animation-delay-300">
            Empowering minds, shaping futures through innovative education and technology
          </p>
          <div class="flex items-center justify-center animate-fade-in-up animation-delay-600">
            <div class="w-24 h-1 bg-gradient-to-r from-orange-400 to-blue-400 rounded-full animate-pulse"></div>
          </div>

          <!-- Call to Action Buttons -->
          <div class="mt-8 flex flex-col sm:flex-row gap-4 justify-center animate-fade-in-up animation-delay-900">
            <button class="bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 rounded-lg font-semibold transition duration-300 transform hover:scale-105 shadow-lg">
              Explore Our Campus
            </button>
            <button class="border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-3 rounded-lg font-semibold transition duration-300 transform hover:scale-105">
              Virtual Tour
            </button>
          </div>
        </div>
      </div>

      <!-- Mission and Vision -->
      <div class="grid lg:grid-cols-2 gap-8 mb-12">
        <div class="mission-card">
          <div class="flex items-center mb-4">
            <svg class="w-8 h-8 text-orange-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
            <h2 class="text-3xl font-bold text-gray-900">Our Mission</h2>
          </div>
          <p class="text-lg text-gray-700 leading-relaxed">
            <strong>"Your Education… Our Mission"</strong><br><br>
            At Benedicto College, we are committed to providing exceptional educational experiences that prepare students for success in an ever-evolving world. Our mission is to foster academic excellence, critical thinking, and personal growth through innovative teaching methods and cutting-edge technology.
          </p>
        </div>

        <div class="vision-card">
          <div class="flex items-center mb-4">
            <svg class="w-8 h-8 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
            <h2 class="text-3xl font-bold text-gray-900">Our Vision</h2>
          </div>
          <p class="text-lg text-gray-700 leading-relaxed">
            To be a globally competitive institution in the Asia-Pacific region, recognized for academic excellence, innovation, and the development of well-rounded individuals who contribute meaningfully to society and the global community.
          </p>
        </div>
      </div>

      <!-- Interactive Timeline Section -->
      <div class="timeline-section mb-16">
        <h2 class="text-4xl font-bold text-center text-gray-900 mb-12">Our Journey</h2>
        <div class="timeline-container relative">
          <!-- Timeline Line -->
          <div class="timeline-line absolute left-1/2 transform -translate-x-1/2 w-1 bg-gradient-to-b from-orange-400 to-blue-400 h-full"></div>

          <!-- Timeline Items -->
          <div class="timeline-item mb-12 relative">
            <div class="timeline-content flex items-center">
              <div class="timeline-left flex-1 text-right pr-8">
                <div class="timeline-card bg-white p-6 rounded-lg shadow-lg border-l-4 border-orange-500 transform hover:scale-105 transition duration-300">
                  <h3 class="text-xl font-bold text-gray-900 mb-2">1985</h3>
                  <h4 class="text-lg font-semibold text-orange-600 mb-2">College Founded</h4>
                  <p class="text-gray-600">Benedicto College was established with a vision to provide quality education in the Philippines.</p>
                </div>
              </div>
              <div class="timeline-marker absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-orange-500 rounded-full border-4 border-white shadow-lg z-10"></div>
              <div class="timeline-right flex-1 pl-8"></div>
            </div>
          </div>

          <div class="timeline-item mb-12 relative">
            <div class="timeline-content flex items-center">
              <div class="timeline-left flex-1 pr-8"></div>
              <div class="timeline-marker absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-500 rounded-full border-4 border-white shadow-lg z-10"></div>
              <div class="timeline-right flex-1 text-left pl-8">
                <div class="timeline-card bg-white p-6 rounded-lg shadow-lg border-r-4 border-blue-500 transform hover:scale-105 transition duration-300">
                  <h3 class="text-xl font-bold text-gray-900 mb-2">1995</h3>
                  <h4 class="text-lg font-semibold text-blue-600 mb-2">First Library Established</h4>
                  <p class="text-gray-600">Our first traditional library was opened, serving thousands of students with physical books and resources.</p>
                </div>
              </div>
            </div>
          </div>

          <div class="timeline-item mb-12 relative">
            <div class="timeline-content flex items-center">
              <div class="timeline-left flex-1 text-right pr-8">
                <div class="timeline-card bg-white p-6 rounded-lg shadow-lg border-l-4 border-orange-500 transform hover:scale-105 transition duration-300">
                  <h3 class="text-xl font-bold text-gray-900 mb-2">2010</h3>
                  <h4 class="text-lg font-semibold text-orange-600 mb-2">Digital Transformation</h4>
                  <p class="text-gray-600">Introduction of digital resources and computer-based catalog systems to modernize library services.</p>
                </div>
              </div>
              <div class="timeline-marker absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-orange-500 rounded-full border-4 border-white shadow-lg z-10"></div>
              <div class="timeline-right flex-1 pl-8"></div>
            </div>
          </div>

          <div class="timeline-item mb-12 relative">
            <div class="timeline-content flex items-center">
              <div class="timeline-left flex-1 pr-8"></div>
              <div class="timeline-marker absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-500 rounded-full border-4 border-white shadow-lg z-10"></div>
              <div class="timeline-right flex-1 text-left pl-8">
                <div class="timeline-card bg-white p-6 rounded-lg shadow-lg border-r-4 border-blue-500 transform hover:scale-105 transition duration-300">
                  <h3 class="text-xl font-bold text-gray-900 mb-2">2020</h3>
                  <h4 class="text-lg font-semibold text-blue-600 mb-2">Online Learning Integration</h4>
                  <p class="text-gray-600">Seamless integration of online learning platforms with our library management system during the pandemic.</p>
                </div>
              </div>
            </div>
          </div>

          <div class="timeline-item relative">
            <div class="timeline-content flex items-center">
              <div class="timeline-left flex-1 text-right pr-8">
                <div class="timeline-card bg-white p-6 rounded-lg shadow-lg border-l-4 border-orange-500 transform hover:scale-105 transition duration-300">
                  <h3 class="text-xl font-bold text-gray-900 mb-2">2025</h3>
                  <h4 class="text-lg font-semibold text-orange-600 mb-2">AI-Powered Library System</h4>
                  <p class="text-gray-600">Launch of our advanced AI-powered library management system with smart recommendations and analytics.</p>
                </div>
              </div>
              <div class="timeline-marker absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-orange-500 rounded-full border-4 border-white shadow-lg z-10"></div>
              <div class="timeline-right flex-1 pl-8"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Library Management System Features -->
      <div class="mb-12">
        <h2 class="text-4xl font-bold text-center text-gray-900 mb-12">Our Library Management System</h2>
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          
          <div class="feature-card">
            <div class="feature-icon">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-3 text-center">Digital Catalog</h3>
            <p class="text-gray-600 text-center">
              Comprehensive digital catalog with advanced search capabilities for books, journals, and digital resources.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-3 text-center">User Management</h3>
            <p class="text-gray-600 text-center">
              Streamlined user registration and management system for students, faculty, and staff members.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-3 text-center">Borrowing System</h3>
            <p class="text-gray-600 text-center">
              Efficient book borrowing and return system with automated notifications and due date tracking.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-3 text-center">Analytics & Reports</h3>
            <p class="text-gray-600 text-center">
              Comprehensive reporting and analytics to track library usage and optimize resource allocation.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-3 text-center">Mobile Access</h3>
            <p class="text-gray-600 text-center">
              Mobile-responsive design allowing access to library services from any device, anywhere.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-3 text-center">Secure & Reliable</h3>
            <p class="text-gray-600 text-center">
              Advanced security measures and reliable infrastructure ensuring data protection and system availability.
            </p>
          </div>
        </div>
      </div>

      <!-- Statistics Section -->
      <div class="stats-section">
        <h2 class="text-3xl font-bold text-center text-gray-900 mb-8">Our Impact</h2>
        <div class="grid md:grid-cols-4 gap-6">
          <div class="stat-item">
            <span class="stat-number">10,000+</span>
            <p class="text-gray-600 font-semibold">Students Served</p>
          </div>
          <div class="stat-item">
            <span class="stat-number">50,000+</span>
            <p class="text-gray-600 font-semibold">Books Available</p>
          </div>
          <div class="stat-item">
            <span class="stat-number">500+</span>
            <p class="text-gray-600 font-semibold">Faculty Members</p>
          </div>
          <div class="stat-item">
            <span class="stat-number">24/7</span>
            <p class="text-gray-600 font-semibold">System Availability</p>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- Footer -->
  <footer class="bg-black py-12 border-t-8 border-orange-500">
    <div class="container mx-auto px-6">
      <!-- Mobile/Tablet: Grid Layout (below 1000px) -->
      <div class="block xl:hidden">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <!-- Get in Touch Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Contact Us</h3>
            <div class="space-y-3 text-gray-300">
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span class="text-sm">Benedicto College Campus, Philippines</span>
              </div>
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <span class="text-sm">info&#64;benedictocollege.edu.ph</span>
              </div>
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <span class="text-sm">+63 (XXX) XXX-XXXX</span>
              </div>
            </div>
          </div>

          <!-- Quick Links Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Quick Links</h3>
            <div class="space-y-2">
              <a routerLink="/about" class="block text-gray-400 hover:text-green-400 transition duration-300">About Us</a>
              <a routerLink="/contact" class="block text-gray-400 hover:text-green-400 transition duration-300">Contact</a>
            </div>
          </div>

          <!-- Connect with Us Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Connect with Us</h3>
            <div class="flex justify-center md:justify-start space-x-4">
              <a href="https://facebook.com/benedictocollege" target="_blank" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="https://benedictocollege.edu.ph" target="_blank" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
                </svg>
              </a>
              <a href="tel:+63321234567" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Desktop: Flexbox Layout (1000px+) -->
      <div class="hidden xl:flex justify-between items-start mb-8">
        <div class="flex-1">
          <h3 class="text-xl font-bold text-white mb-4">Contact Us</h3>
          <div class="space-y-3 text-gray-300">
            <div class="flex items-center">
              <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              <span class="text-sm">Benedicto College Campus, Philippines</span>
            </div>
            <div class="flex items-center">
              <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              <span class="text-sm">info&#64;benedictocollege.edu.ph</span>
            </div>
            <div class="flex items-center">
              <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
            </svg>
              <span class="text-sm">+63 (XXX) XXX-XXXX</span>
            </div>
          </div>
        </div>

        <div class="flex-1">
          <h3 class="text-xl font-bold text-white mb-4">Quick Links</h3>
          <div class="space-y-2">
            <a routerLink="/about" class="block text-gray-400 hover:text-green-400 transition duration-300">About Us</a>
            <a routerLink="/contact" class="block text-gray-400 hover:text-green-400 transition duration-300">Contact</a>
          </div>
        </div>

        <div class="flex-1">
          <h3 class="text-xl font-bold text-white mb-4">Connect with Us</h3>
          <a href="https://facebook.com/benedictocollege" target="_blank" class="text-gray-400 hover:text-green-400 transition duration-300 mr-6">Facebook</a>
          <a href="https://benedictocollege.edu.ph" target="_blank" class="text-gray-400 hover:text-green-400 transition duration-300 mr-6">Website</a>
          <a href="tel:+63321234567" class="text-gray-400 hover:text-green-400 transition duration-300">Phone</a>
        </div>
      </div>

      <!-- Copyright Section -->
      <div class="border-t border-gray-700 pt-6">
        <div class="flex flex-col xl:flex-row justify-between items-center">
          <div class="text-gray-400 mb-4 xl:mb-0 text-center xl:text-left">
            &copy; 2025 Benedicto College Library Management System. All Rights Reserved.
          </div>
          <div class="text-gray-400 text-sm text-center xl:text-right">
            Your Education… Our Mission
          </div>
        </div>
      </div>
    </div>
  </footer>
</div>

<script>
  // Enhanced header functionality
  document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
      mobileMenuButton.addEventListener('click', function() {
        mobileMenu.classList.toggle('hidden');

        // Toggle hamburger icon
        const icon = mobileMenuButton.querySelector('svg');
        if (mobileMenu.classList.contains('hidden')) {
          icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path>';
        } else {
          icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>';
        }
      });
    }

    // Login dropdown functionality
    const loginDropdownButton = document.getElementById('login-dropdown-button');
    const loginDropdown = document.getElementById('login-dropdown');

    if (loginDropdownButton && loginDropdown) {
      loginDropdownButton.addEventListener('click', function(e) {
        e.stopPropagation();
        loginDropdown.classList.toggle('hidden');
      });

      // Close dropdown when clicking outside
      document.addEventListener('click', function(e) {
        if (!loginDropdownButton.contains(e.target) && !loginDropdown.contains(e.target)) {
          loginDropdown.classList.add('hidden');
        }
      });

      // Close dropdown on escape key
      document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
          loginDropdown.classList.add('hidden');
        }
      });
    }
  });
</script>

/* About page specific styles */
.about-container {
  max-width: 1200px;
  margin: 0 auto;
}

.hero-section {
  background: linear-gradient(135deg, #1f2937, #374151);
  border-radius: 16px;
  padding: 4rem 2rem;
  margin-bottom: 4rem;
  color: white;
  text-align: center;
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Animation keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation classes */
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
  opacity: 0;
}

.animate-slide-in-left {
  animation: slideInLeft 0.8s ease-out forwards;
  opacity: 0;
}

.animate-slide-in-right {
  animation: slideInRight 0.8s ease-out forwards;
  opacity: 0;
}

.animate-count-up {
  animation: countUp 1s ease-out forwards;
  opacity: 0;
}

/* Animation delays */
.animation-delay-300 {
  animation-delay: 0.3s;
}

.animation-delay-600 {
  animation-delay: 0.6s;
}

.animation-delay-900 {
  animation-delay: 0.9s;
}

.animation-delay-1200 {
  animation-delay: 1.2s;
}

.mission-card, .vision-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.mission-card {
  border-left: 6px solid #f97316;
}

.vision-card {
  border-left: 6px solid #3b82f6;
}

.feature-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #f97316, #ea580c);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.stats-section {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 16px;
  padding: 3rem 2rem;
  margin: 3rem 0;
}

.stat-item {
  text-align: center;
  padding: 1rem;
}

.stat-number {
  font-size: 3rem;
  font-weight: bold;
  color: #f97316;
  display: block;
  transition: all 0.3s ease;
}

.stat-number:hover {
  transform: scale(1.1);
  color: #ea580c;
}

/* Timeline Styles */
.timeline-section {
  position: relative;
  padding: 2rem 0;
}

.timeline-container {
  max-width: 1000px;
  margin: 0 auto;
  position: relative;
}

.timeline-line {
  background: linear-gradient(to bottom, #f97316, #3b82f6);
  border-radius: 2px;
}

.timeline-item {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.6s ease;
}

.timeline-item.animate {
  opacity: 1;
  transform: translateY(0);
}

.timeline-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.timeline-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.timeline-marker {
  transition: all 0.3s ease;
}

.timeline-marker:hover {
  transform: translate(-50%, -50%) scale(1.5);
}

/* Parallax and Scroll Effects */
.parallax-element {
  transition: transform 0.1s ease-out;
}

/* Enhanced Feature Cards */
.feature-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  opacity: 0;
  transform: translateY(50px);
}

.feature-card.animate {
  opacity: 1;
  transform: translateY(0);
}

.feature-card:hover {
  transform: translateY(-10px) scale(1.03);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

/* Loading States */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Interactive Feature Cards */
.feature-card.expanded {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 20px 40px rgba(249, 115, 22, 0.15);
}

.feature-details {
  transition: all 0.3s ease;
}

/* Responsive Design Improvements */
@media (max-width: 1024px) {
  .timeline-container {
    padding: 0 1rem;
  }

  .timeline-left, .timeline-right {
    flex: none;
    width: 100%;
    text-align: center !important;
    padding: 0 !important;
  }

  .timeline-line {
    left: 2rem !important;
  }

  .timeline-marker {
    left: 2rem !important;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 2rem 1rem;
    min-height: 50vh;
  }

  .hero-section h1 {
    font-size: 2.5rem;
  }

  .hero-section p {
    font-size: 1.125rem;
  }

  .mission-card, .vision-card, .feature-card {
    padding: 1.5rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .timeline-item {
    margin-bottom: 2rem;
  }

  .timeline-card {
    margin: 0 1rem;
  }
}

/* Accessibility Improvements */
.feature-card:focus {
  outline: 2px solid #f97316;
  outline-offset: 2px;
}

.timeline-marker:focus {
  outline: 2px solid #f97316;
  outline-offset: 4px;
}

/* Reduced motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in-up,
  .animate-slide-in-left,
  .animate-slide-in-right,
  .animate-count-up {
    animation: none;
    opacity: 1;
    transform: none;
  }

  .feature-card,
  .timeline-card,
  .timeline-marker {
    transition: none;
  }
}

/* Print styles */
@media print {
  .hero-section video {
    display: none;
  }

  .feature-details {
    display: block !important;
  }

  .timeline-line {
    background: #000 !important;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 2rem 1rem;
  }
  
  .mission-card, .vision-card, .feature-card {
    padding: 1.5rem;
  }
  
  .stat-number {
    font-size: 2rem;
  }
}
